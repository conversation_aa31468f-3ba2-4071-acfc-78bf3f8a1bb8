package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.model.sys.Page;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.Map;

public class DefaultPageRepository extends PageRepository {

    public DefaultPageRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            PermissionRepository permissionRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Override
    public Map<String, String[]> readPagePermissionMappings() {
        Map<String, String[]> _maps = super.readPagePermissionMappings();

        return _maps;
    }

    @Override
    protected void customize(List<Page> records) {
        super.customize(records);
    }

    @Override
    protected void updateBuildIn(List<Page> records) {
        super.updateBuildIn(records);
    }

}