package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.service.wx.ma.lang.User;
import com.chinamobile.sparrow.domain.util.DefaultValidatorUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Objects;

public class DefaultUserRepository extends UserRepository<DefaultUser> {

    final AccessFacade accessFacade;

    public DefaultUserRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository,
            String passwordConstraint,
            String rsaPrivateKey,
            AccessFacade accessFacade
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, passwordConstraint, rsaPrivateKey, DefaultUser.class);
        this.accessFacade = accessFacade;
    }

    @Transactional(readOnly = true)
    public Result<DefaultUser> getBriefInWxMa(String openId, String mp, Boolean isEnabled) {
        Result<DefaultUser> _record = new Result<>();

        _record.data = stream(DefaultUser.class)
                .where(i -> mp.equals(i.getMp()) || openId.equals(i.getMaOpenId()))
                .where(i -> isEnabled == null || isEnabled == i.getIsEnabled())
                .sortedDescendingBy(DefaultUser::getMaOpenId)
                .findFirst().orElse(null);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DefaultUser.class.getSimpleName()});
            return _record;
        }

        return _record;
    }

    @Override
    public Result<String> save(DefaultUser record, String actorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<DefaultUser> _record = ((DefaultUserRepository) AopContext.currentProxy()).getBriefByIdOrAccountOrMp(record.getId(), true);
        if (_record.isOK()) {
            if (!DefaultValidatorUtil.isIdentityNo(record.getIdNO())) {
                _id.setCode(Result.ENUM_ERROR.P, 4);
                return _id;
            }

            if (!DefaultValidatorUtil.isChineseName(record.getName())) {
                _id.setCode(Result.ENUM_ERROR.P, 5);
                return _id;
            }

            if (!DefaultValidatorUtil.isMp(record.getMp())) {
                _id.setCode(Result.ENUM_ERROR.P, 6);
                return _id;
            }
        } else {
            _record.data = new DefaultUser();

            _alreadyExisted = false;
        }

        String _identifier = _record.data.getId();
        JinqStream<DefaultUser> _query = stream(DefaultUser.class).where(i -> _identifier == null || !_identifier.equals(i.getId()));

        String _account = record.getAccount();
        String _mp = record.getMp();
        if (_query.where(i -> _account.equals(i.getAccount()) || (_mp != null && _mp.equals(i.getMp())))
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        if (StringUtils.hasLength(record.getDeptId())) {
            _record.data.setDeptId(record.getDeptId());
        }

        ((DefaultUserRepository) AopContext.currentProxy()).copyProperties(record, _record.data, new String[]{"id", "password", "isEnabled", "isLocked", "loginAttempts", "deptId", "yzyId", "maOpenId", "unionId", "isOnline"});

        Result<Void> _success = _alreadyExisted ? update(_record.data, actorId) : add(_record.data, actorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

    @Transactional(readOnly = true)
    public ImmutablePair<Boolean, String> binding(String code, String operatorId) throws WxErrorException {
        User _user = accessFacade.getUser(code);

        Result<DefaultUser> _operator = ((DefaultUserRepository) AopContext.currentProxy()).getBriefInWxMa(_user.openId, null, true);
        if (!_operator.isOK()) {
            return ImmutablePair.of(false, null);
        }

        return Objects.equals(_operator.data.getId(), operatorId) ? ImmutablePair.of(true, _operator.data.getAccount()) : ImmutablePair.of(false, _operator.data.getAccount());
    }

    public Result<Boolean> bindMaOpenIdAndUnionId(String code, String operatorId) throws WxErrorException {
        Result<Boolean> _bind = new Result<>();

        Result<DefaultUser> _record = ((DefaultUserRepository) AopContext.currentProxy()).getByIdOrAccountOrMp(operatorId, true);
        if (!_record.isOK()) {
            return _bind.pack(_record);
        }

        if (StringUtils.hasLength(_record.data.getMaOpenId())) {
            _record.data.setMaOpenId(null);
            _record.data.setUnionId(null);
        } else {
            User _user = accessFacade.getUser(code);
            _record.data.setMaOpenId(_user.openId);
            _record.data.setUnionId(_user.unionId);
        }

        super.update(_record.data, operatorId);

        _bind.data = StringUtils.hasLength(_record.data.getMaOpenId());
        return _bind;
    }

}