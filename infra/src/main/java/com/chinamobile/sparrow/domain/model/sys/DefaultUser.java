package com.chinamobile.sparrow.domain.model.sys;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

@Entity
@DiscriminatorValue(value = "Default")
public class DefaultUser extends User {

    @Column(columnDefinition = "varchar(36) unique")
    String idNO;

    @Column(columnDefinition = "varchar(36) unique")
    String maOpenId;

    @Column(columnDefinition = "varchar(36) unique")
    String unionId;

    public String getIdNO() {
        return idNO;
    }

    public void setIdNO(String idNO) {
        this.idNO = idNO;
    }

    public String getMaOpenId() {
        return maOpenId;
    }

    public void setMaOpenId(String maOpenId) {
        this.maOpenId = maOpenId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String globalId) {
        this.unionId = globalId;
    }

}