# 数据库设计

## 矛盾纠纷表 (disputes)
```sql
CREATE TABLE disputes (
    id VARCHAR(36) PRIMARY KEY,
    category_id VARCHAR(36) NOT NULL COMMENT '纠纷类型ID',
    amount DECIMAL COMMENT '涉事金额',
    level INTEGER COMMENT '纠纷等级',
    involved_in_law BOOLEAN COMMENT '是否涉法',
    region_id VARCHAR(36) COMMENT '区域ID',
    description TEXT NOT NULL COMMENT '纠纷描述',
    location TEXT NOT NULL COMMENT '发生地点',
    time DATETIME NOT NULL COMMENT '发生时间',
    status ENUM('DRAFT', 'WAITED', 'PROCESSING', 'RESOLVED') DEFAULT 'DRAFT' COMMENT '处理状态',
    created_time DATETIME,
    updated_time DATETIME,
    created_by VARCHAR(36),
    updated_by VARCHAR(36)
);
```

## 纠纷参与人表 (dispute_participants)
```sql
CREATE TABLE dispute_participants (
    id VARCHAR(36) PRIMARY KEY,
    dispute_id VARCHAR(36) NOT NULL COMMENT '纠纷ID',
    id_no VARCHAR(36) NOT NULL COMMENT '身份证号',
    name VARCHAR(36) NOT NULL COMMENT '姓名',
    contact VARCHAR(36) NOT NULL COMMENT '联系方式',
    created_time DATETIME,
    updated_time DATETIME,
    created_by VARCHAR(36),
    updated_by VARCHAR(36),
    FOREIGN KEY (dispute_id) REFERENCES disputes(id)
);
```

## 综治中心 (centers)
```sql
CREATE TABLE centers (
    id VARCHAR(36) PRIMARY KEY,
    region VARCHAR(36) NOT NULL COMMENT '所属区域',
    name VARCHAR(36) NOT NULL COMMENT '中心名称',
    contact VARCHAR(36) NOT NULL COMMENT '联系方式',
    lng FLOAT COMMENT '经度',
    lat FLOAT COMMENT '纬度',
    location TEXT NOT NULL COMMENT '详细地址',
    avatar_id VARCHAR(36) NOT NULL COMMENT '头像ID',
    created_time DATETIME,
    updated_time DATETIME,
    created_by VARCHAR(36),
    updated_by VARCHAR(36)
);
```

## 行政区划 (sys_departments)
继承自框架的Department表，扩展字段：
- `area`: 管辖面积
- `description`: 机构描述

# 功能设计

## 系统概述
本系统是一个矛盾纠纷调解管理平台，主要用于管理和处理各类社会矛盾纠纷，提供从纠纷登记、分派、调解到结案的全流程管理。

## 核心功能模块

### 1. 用户认证与授权
- **多种登录方式**：
  - 用户名密码登录
  - 短信验证码登录
  - 微信小程序授权登录
  - 微信小程序手机号登录
- **权限管理**：基于Apache Shiro的RBAC权限控制
- **安全特性**：
  - RSA加密传输
  - 验证码防护
  - 登录失败次数限制
  - 密码强度验证

### 2. 矛盾纠纷管理
- **纠纷登记**：
  - 支持纠纷基本信息录入
  - 涉事金额、人数统计
  - 纠纷等级评定
  - 是否涉法标识
- **纠纷分类**：
  - 动态纠纷类型管理
  - 支持多级分类体系
- **参与人管理**：
  - 申请人和被申请人信息
  - 身份证号验证
  - 联系方式管理
- **纠纷处理**：
  - 状态流转：草稿→待处理→处理中→已解决
  - 处理过程记录
  - 结案管理

### 3. 调解中心管理
- **中心信息**：
  - 基本信息维护
  - 地理位置定位
  - 联系方式管理
- **区域管理**：
  - 按区域查询中心
  - 地理位置服务

### 4. 组织机构管理
- **机构树形结构**：
  - 支持多级机构管理
  - 村居级别（Level 3）
  - 网格级别（Level 4）
  - 应急单元级别（Level 5）
- **人员管理**：
  - 工作人员信息维护
  - 角色权限分配

### 5. 系统管理
- **用户管理**：
  - 用户信息维护
  - 密码管理
  - 账号状态控制
- **角色权限**：
  - 角色定义
  - 权限分配
  - 菜单管理
- **数据字典**：
  - 纠纷类型配置
  - 系统参数设置

### 6. 文件管理
- **文件上传**：
  - 支持多种文件格式
  - 文件大小限制
  - 安全检查
- **存储方式**：
  - 本地文件系统
  - 对象存储（S3兼容）

### 7. 微信小程序集成
- **小程序授权**：
  - 微信登录
  - 用户信息获取
  - 手机号绑定
- **业务功能**：
  - 移动端纠纷上报
  - 处理进度查询

# API设计

## API架构
- **协议**：HTTP/HTTPS
- **数据格式**：JSON
- **请求方式**：统一使用POST
- **认证方式**：Session + Shiro安全框架

## 统一响应格式
```json
{
    "code": 0,           // 响应码，0表示成功
    "message": "成功",    // 响应消息
    "data": {}           // 响应数据
}
```

## 核心API接口

### 1. 用户认证接口

#### 1.1 用户名密码登录
- **接口地址**：`POST /sec/login`
- **请求参数**：
```json
{
    "account": "用户名",
    "password": "加密后的密码",
    "captcha": "验证码"
}
```
- **响应数据**：
```json
{
    "code": 0,
    "data": "登录成功标识"
}
```

#### 1.2 微信小程序登录
- **接口地址**：`POST /sec/login/mini-app`
- **请求参数**：
```json
{
    "code": "微信授权码"
}
```
- **响应数据**：
```json
{
    "code": 0,
    "data": {
        "id": "用户ID",
        "name": "用户姓名",
        "account": "账号"
    }
}
```

#### 1.3 微信小程序手机号登录
- **接口地址**：`POST /sec/login/mini-app/phone-number`
- **请求参数**：
```json
{
    "code": "授权码",
    "encryptedData": "加密数据",
    "iv": "初始向量"
}
```

### 2. 矛盾纠纷管理接口

#### 2.1 获取纠纷类型
- **接口地址**：`POST /dispute/categories`
- **请求参数**：无
- **响应数据**：
```json
{
    "code": 0,
    "data": [
        {
            "id": "类型ID",
            "name": "类型名称"
        }
    ]
}
```

#### 2.2 查询纠纷详情
- **接口地址**：`POST /dispute/get`
- **请求参数**：
```json
{
    "id": "纠纷ID"
}
```
- **响应数据**：
```json
{
    "code": 0,
    "data": {
        "id": "纠纷ID",
        "categoryId": "类型ID",
        "category": "类型名称",
        "amount": 10000.00,
        "level": 1,
        "involvedInLaw": false,
        "regionId": "区域ID",
        "region": "区域名称",
        "description": "纠纷描述",
        "location": "发生地点",
        "time": "2024-01-01 10:00:00",
        "status": "DRAFT"
    }
}
```

#### 2.3 查询我的纠纷列表
- **接口地址**：`POST /dispute/me`
- **请求参数**：
```json
{
    "count": 10,
    "index": 1,
    "regionId": "区域ID",
    "categoryId": "类型ID",
    "description": "关键词",
    "location": "地点关键词",
    "from": "2024-01-01",
    "to": "2024-12-31",
    "status": "DRAFT"
}
```
- **响应数据**：
```json
{
    "code": 0,
    "data": {
        "records": [],
        "totalCount": 100,
        "pageCount": 10
    }
}
```

#### 2.4 保存纠纷信息
- **接口地址**：`POST /dispute/save`
- **请求参数**：
```json
{
    "id": "纠纷ID（新增时可为空）",
    "categoryId": "类型ID",
    "amount": 10000.00,
    "level": 1,
    "involvedInLaw": false,
    "regionId": "区域ID",
    "description": "纠纷描述",
    "location": "发生地点",
    "time": "2024-01-01 10:00:00",
    "participants": [
        {
            "idNO": "身份证号",
            "name": "姓名",
            "contact": "联系方式"
        }
    ]
}
```
- **响应数据**：
```json
{
    "code": 0,
    "data": "纠纷ID"
}
```

#### 2.5 删除纠纷
- **接口地址**：`POST /dispute/remove`
- **请求参数**：
```json
{
    "id": "纠纷ID"
}
```

### 3. 调解中心接口

#### 3.1 查询调解中心
- **接口地址**：`POST /center/find`
- **请求参数**：
```json
{
    "region": "区域（可选）"
}
```
- **响应数据**：
```json
{
    "code": 0,
    "data": [
        {
            "id": "中心ID",
            "region": "所属区域",
            "name": "中心名称",
            "contact": "联系方式",
            "lng": 120.123456,
            "lat": 30.123456,
            "location": "详细地址",
            "avatarId": "头像ID"
        }
    ]
}
```

### 4. 组织机构接口

#### 4.1 获取机构树
- **接口地址**：`POST /agency/tree`
- **请求参数**：无
- **响应数据**：
```json
{
    "code": 0,
    "data": [
        {
            "id": "机构ID",
            "name": "机构名称",
            "level": 1,
            "children": []
        }
    ]
}
```

### 5. 用户管理接口

#### 5.1 保存用户信息
- **接口地址**：`POST /save/default`
- **请求参数**：
```json
{
    "name": "姓名",
    "idNO": "身份证号",
    "mp": "手机号",
    "email": "邮箱"
}
```

#### 5.2 绑定微信小程序
- **接口地址**：`POST /bind/mini-app`
- **请求参数**：
```json
{
    "code": "微信授权码"
}
```

### 6. 文件管理接口

#### 6.1 文件上传
- **接口地址**：`POST /media/record`
- **请求方式**：multipart/form-data
- **支持格式**：txt,doc,docx,xls,xlsx,ppt,pptx,ofd,pdf,avif,bmp,gif,ico,jfif,jpeg,jpg,jxl,pjp,pjpeg,png,svg,svgz,tif,tiff,webp,xbm,asx,avi,m4v,mov,mp4,mpeg,mpg,ogm,ogv,webm,wmv,rar,zip

#### 6.2 文件下载
- **接口地址**：`GET /media/reader/{fileId}`

## 错误码说明
- **0**：成功
- **1xx**：参数错误
- **2xx**：业务逻辑错误
- **3xx**：权限错误
- **4xx**：系统错误
- **5xx**：网络错误

## 安全机制
1. **数据加密**：敏感数据使用RSA加密传输
2. **权限验证**：基于Shiro的权限注解控制
3. **参数校验**：使用Validation框架进行参数验证
4. **SQL注入防护**：使用JPA/Hibernate预编译语句
5. **XSS防护**：前端数据过滤和转义