package cn.gov.st.plc.cmc.szz.controller;

import cn.gov.st.plc.cmc.szz.model.Center;
import cn.gov.st.plc.cmc.szz.repository.CenterRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.google.gson.JsonObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "center")
public class CenterController {

    final CenterRepository centerRepository;

    public CenterController(CenterRepository centerRepository) {
        this.centerRepository = centerRepository;
    }

    @PostMapping(value = "/find")
    public Result<List<Center>> find(@RequestBody JsonObject data) {
        String _region = Optional.ofNullable(data.get("region"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Center>> _records = new Result<>();
        _records.data = centerRepository.find(_region);
        return _records;
    }

}