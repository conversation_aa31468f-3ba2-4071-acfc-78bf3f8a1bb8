package cn.gov.st.plc.cmc.szz;

import com.chinamobile.sparrow.domain.infra.orm.jing.MySQLFunctions;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.shardingsphere.driver.api.yaml.YamlShardingSphereDataSourceFactory;
import org.hibernate.SessionFactory;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.hibernate5.HibernateTransactionManager;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Date;
import java.util.Properties;

@Configuration
public class DataSourceAutoConfiguration {

    @Bean
    public DataSource mainDataSource(@Value(value = "${spring.datasource.main.config}") String path) throws IOException, SQLException {
        File _yaml = ResourceUtils.getFile(path);
        return YamlShardingSphereDataSourceFactory.createDataSource(_yaml);
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.main.properties")
    public Properties mainDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean mainSessionFactory(@Value(value = "${spring.datasource.main.packages}") String packages, @Qualifier(value = "mainDataSource") DataSource dataSource, @Qualifier(value = "mainDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _sessionFactoryBean = new LocalSessionFactoryBean();
        _sessionFactoryBean.setDataSource(dataSource);

        String[] _packages = StringUtils.hasLength(packages)
                ? Arrays.stream(packages.split(","))
                .map(String::trim)
                .toArray(String[]::new)
                : new String[]{"com.chinamobile.sparrow.domain.model"};
        _sessionFactoryBean.setPackagesToScan(_packages);

        _sessionFactoryBean.setHibernateProperties(properties);

        return _sessionFactoryBean;
    }

    @Bean
    @Primary
    public PlatformTransactionManager mainTransactionManager(@Qualifier(value = "mainSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider mainJinqJPAStreamProvider(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());

        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "date_format");
        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("hour", Date.class), "hour");
        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("minute", Date.class), "minute");

        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("jsonContains", String.class, String.class), "json_contains");
        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("jsonExtract", String.class, String.class), "json_extract");

        return _provider;
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.cmc")
    public DataSource cmcDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.cmc.properties")
    public Properties cmcDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean cmcSessionFactory(@Qualifier(value = "cmcDataSource") DataSource dataSource, @Qualifier(value = "cmcDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _sessionFactoryBean = new LocalSessionFactoryBean();
        _sessionFactoryBean.setDataSource(dataSource);
        _sessionFactoryBean.setPackagesToScan("cn.gov.st.plc.cmc.admin.model");
        _sessionFactoryBean.setHibernateProperties(properties);

        return _sessionFactoryBean;
    }

    @Bean
    public PlatformTransactionManager cmcTransactionManager(@Qualifier(value = "cmcSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider cmcJinqJPAStreamProvider(@Qualifier(value = "cmcSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());

        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "date_format");
        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("hour", Date.class), "hour");
        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("minute", Date.class), "minute");

        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("jsonContains", String.class, String.class), "json_contains");
        _provider.registerCustomSqlFunction(MySQLFunctions.class.getDeclaredMethod("jsonExtract", String.class, String.class), "json_extract");

        return _provider;
    }

}