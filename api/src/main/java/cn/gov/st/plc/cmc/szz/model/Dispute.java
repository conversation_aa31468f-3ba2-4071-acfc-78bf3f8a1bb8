package cn.gov.st.plc.cmc.szz.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "disputes")
public class Dispute extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36, nullable = false)
    String categoryId;

    @Transient
    String category;

    // 涉事金额
    BigDecimal amount;

    // 涉事人数
    @Transient
    int number;

    Integer level;

    Boolean involvedInLaw;

    @Column(length = 36)
    String regionId;

    @Transient
    String region;

    @Column(columnDefinition = "text", nullable = false)
    String description;

    @Column(columnDefinition = "text", nullable = false)
    String location;

    @Column(nullable = false)
    Date time;

    ENUM_STATUS status = ENUM_STATUS.DRAFT;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getInvolvedInLaw() {
        return involvedInLaw;
    }

    public void setInvolvedInLaw(Boolean involvedInLaw) {
        this.involvedInLaw = involvedInLaw;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = StringUtils.trimWhitespace(description);
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = StringUtils.trimWhitespace(location);
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public enum ENUM_STATUS {
        DRAFT, WAITED, ACCEPTED, FINISHED, NOT_ACCEPTED
    }

}