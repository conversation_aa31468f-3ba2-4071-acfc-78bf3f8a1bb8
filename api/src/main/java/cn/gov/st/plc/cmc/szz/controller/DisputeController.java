package cn.gov.st.plc.cmc.szz.controller;

import cn.gov.st.plc.cmc.szz.model.Dispute;
import cn.gov.st.plc.cmc.szz.model.Participant;
import cn.gov.st.plc.cmc.szz.repository.DisputeRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "dispute")
public class DisputeController {

    final DisputeRepository disputeRepository;
    final LoginUtil loginUtil;

    public DisputeController(
            DisputeRepository disputeRepository,
            LoginUtil loginUtil
    ) {
        this.disputeRepository = disputeRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/categories")
    public Result<DisputeRepository.Category[]> categories() {
        Result<DisputeRepository.Category[]> _categories = new Result<>();
        _categories.data = disputeRepository.categories();
        return _categories;
    }

    @PostMapping(value = "/get")
    public Result<Dispute> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        return disputeRepository.get(_id);
    }

    @PostMapping(value = "/me")
    public Result<PaginatedRecords<Dispute>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _regionId = data.get("regionId").getAsString();
        String _categoryId = data.get("categoryId").getAsString();
        String _description = data.get("description").getAsString();
        String _location = data.get("location").getAsString();
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Dispute.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Dispute.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);

        Result<PaginatedRecords<Dispute>> _page = new Result<>();
        _page.data = disputeRepository.search(_count, _index, _regionId, _categoryId, _description, _location, _from, _to, _status, loginUtil.getUserId());
        return _page;
    }

    @PostMapping(value = "/save")
    public Result<String> save(@RequestBody Dispute record) {
        return disputeRepository.save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    public Result<Void> remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        return disputeRepository.remove(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/participant/get")
    public Result<Participant> accept(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        return disputeRepository.getParticipant(_id);
    }

    @PostMapping(value = "/participant/find")
    public Result<List<Participant>> participants(String disputeId) {
        Result<List<Participant>> _participants = new Result<>();
        _participants.data = disputeRepository.participants(disputeId);
        return _participants;
    }

    @PostMapping(value = "/participant/save")
    public Result<String> saveParticipant(@RequestBody Participant participant) {
        return disputeRepository.saveParticipant(participant, loginUtil.getUserId());
    }

    @PostMapping(value = "/participant/remove")
    public Result<Void> removeParticipant(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        return disputeRepository.removeParticipant(_id, loginUtil.getUserId());
    }

}