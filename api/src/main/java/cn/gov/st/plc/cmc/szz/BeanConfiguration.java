package cn.gov.st.plc.cmc.szz;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.gov.st.plc.cmc.admin.repository.sys.AgencyRepository;
import cn.gov.st.plc.cmc.admin.repository.sys.StaffRepository;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.PageRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.service.wx.ma.DefaultAccessFacade;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultRecordController;
import com.chinamobile.sparrow.springboot.web.controller.media.ReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.RecordController;
import com.chinamobile.sparrow.springboot.web.controller.sec.DefaultLoginController;
import com.chinamobile.sparrow.springboot.web.controller.sec.LoginController;
import com.chinamobile.sparrow.springboot.web.controller.sys.*;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.persistence.EntityManagerFactory;

@Configuration
public class BeanConfiguration {

    @Bean(value = "wxMaAccessFacade")
    @ConditionalOnProperty(value = "wx.ma.enabled", havingValue = "true")
    public AccessFacade accessFacade(WxMaService wxMaService) {
        return new DefaultAccessFacade(wxMaService);
    }

    @Bean
    public StaffRepository staffRepository(
            @Qualifier(value = "cmcSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "cmcJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider
    ) {
        return new StaffRepository(entityManagerFactory, jinqJPAStreamProvider);
    }

    @Bean
    public AgencyRepository agencyRepository(
            @Qualifier(value = "cmcSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "cmcJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            StaffRepository staffRepository
    ) {
        return new AgencyRepository(entityManagerFactory, jinqJPAStreamProvider, staffRepository);
    }

    @Bean
    public UserRepository<?> userRepository(
            @Value(value = "${sec.password-constraint}") String passwordConstraint,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade accessFacade
    ) {
        return new DefaultUserRepository(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, passwordConstraint, rsaPrivateKey, accessFacade);
    }

    @Bean
    public DepartmentRepository<?> departmentRepository(
            @Qualifier("mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier("mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository<?> userRepository
    ) {
        return new DepartmentRepository<>(entityManagerFactory, jinqJPAStreamProvider, Department.class, userRepository);
    }

    @Bean
    public LoginController loginController(
            @Value(value = "${sec.captcha}") boolean captcha,
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            VerificationCodeRepository verificationCodeRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade wxMaAccessFacade,
            LoginUtil loginUtil
    ) {
        return new DefaultLoginController(captcha, rsaPublicKey, rsaPrivateKey, verificationCodeRepository, null, null, null, wxMaAccessFacade, loginUtil);
    }

    @Bean
    public ProfileController profileController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultProfileController(userRepository, loginUtil);
    }

    @Bean
    public SettingController settingController(
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            DefaultUserRepository userRepository,
            DepartmentRepository<?> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            PageRepository pageRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultSettingController(rsaPublicKey, userRepository, departmentRepository, roleRepository, permissionRepository, pageRepository, loginUtil);
    }

    @Bean
    public ReaderController readerController(
            AbstractMediaRepository mediaRepository,
            RecordController recordController,
            LoginUtil loginUtil
    ) {
        return new DefaultReaderController(mediaRepository, recordController, loginUtil);
    }

    @Bean
    public RecordController recordController(
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultRecordController(mediaRepository, loginUtil);
    }

    @Bean
    public UserController userController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultUserController(userRepository, loginUtil);
    }

}