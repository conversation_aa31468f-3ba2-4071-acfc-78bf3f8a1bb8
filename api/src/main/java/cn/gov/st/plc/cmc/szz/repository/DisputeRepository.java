package cn.gov.st.plc.cmc.szz.repository;

import cn.gov.st.plc.cmc.szz.model.Dispute;
import cn.gov.st.plc.cmc.szz.model.Participant;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.DefaultValidatorUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;

@Repository
@ErrorCode(module = "001")
public class DisputeRepository extends AbstractEntityRepository<Dispute> {

    final String CATEGORY_ID = "矛盾纠纷";
    final String CATEGORY_NAME = "事项类型";

    final DefaultUserRepository userRepository;
    final DictionaryRepository dictionaryRepository;

    public DisputeRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            DefaultUserRepository userRepository,
            DictionaryRepository dictionaryRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Dispute.class);
        this.userRepository = userRepository;
        this.dictionaryRepository = dictionaryRepository;
    }

    @Transactional(readOnly = true)
    public Result<Dispute> get(String id) {
        Result<Dispute> _record = new Result<>();

        _record.data = getCurrentSession().get(Dispute.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Dispute.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<Dispute> permit(String id, String userId) {
        Result<Dispute> _record = get(id);
        if (!_record.isOK()) {
            return _record;
        }

        if (!Objects.equals(_record.data.getCreatorId(), userId)) {
            _record.setCode(Result.DATA_ACCESS_DENY);
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Dispute> search(int count, int index, String regionId, String categoryId, String description, String location, Date from, Date to, Dispute.ENUM_STATUS status, String userId) {
        JinqStream<Dispute> _query = stream(Dispute.class);

        if (StringUtils.hasLength(regionId)) {
            _query = _query.where(i -> regionId.equals(i.getRegionId()));
        }

        if (StringUtils.hasLength(categoryId)) {
            _query = _query.where(i -> categoryId.equals(i.getCategoryId()));
        }

        if (StringUtils.hasLength(description)) {
            _query = _query.where(i -> i.getDescription().contains(description));
        }

        if (StringUtils.hasLength(location)) {
            _query = _query.where(i -> i.getLocation().contains(location));
        }

        if (from != null) {
            _query = _query.where(i -> !i.getTime().before(from));
        }

        if (to != null) {
            Date _to = DateUtil.addDays(to, 1);
            _query = _query.where(i -> i.getTime().before(_to));
        }

        if (status != null) {
            _query = _query.where(i -> status == i.getStatus());
        }

        if (StringUtils.hasLength(userId)) {
            _query = _query.where(i -> userId.equals(i.getCreatorId()));
        }

        PaginatedRecords<Dispute> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public Result<String> save(Dispute record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<Dispute> _record = ((DisputeRepository) AopContext.currentProxy()).permit(record.getId(), operatorId);
        if (!_record.isOK() && !_record.getCode().endsWith(Result.DATABASE_RECORD_NOT_FOUND)) {
            return _id.pack(_record);
        }

        if (!_record.isOK()) {
            _record.data = new Dispute();

            _alreadyExisted = false;
        } else if (!Arrays.asList(Dispute.ENUM_STATUS.DRAFT, Dispute.ENUM_STATUS.WAITED).contains(_record.data.getStatus())) {
            _record.setCode(Result.ENUM_ERROR.B, 1);
            return _id.pack(_record);
        }

        ((DisputeRepository) AopContext.currentProxy()).copyProperties(record, _record.data, new String[]{"id"});

        if (_alreadyExisted) {
            super.update(record, operatorId);
        } else {
            super.add(record, operatorId);
        }

        _id.data = record.getId();
        return _id;
    }

    public Result<Void> remove(String id, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<Dispute> _record = ((DisputeRepository) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        if (!Arrays.asList(Dispute.ENUM_STATUS.DRAFT, Dispute.ENUM_STATUS.WAITED).contains(_record.data.getStatus())) {
            _record.setCode(Result.ENUM_ERROR.B, 1);
            return _success.pack(_record);
        }

        getCurrentSession().remove(_record.data);

        return _success;
    }

    @Transactional(readOnly = true)
    public Category[] categories() {
        String _category = dictionaryRepository.getVal(CATEGORY_ID, CATEGORY_NAME, true);
        return StringUtils.hasLength(_category) ? ConverterUtil.json2Object(_category, Category[].class) : new Category[0];
    }

    @Transactional(readOnly = true)
    public Result<Participant> getParticipant(String id) {
        Result<Participant> _participant = new Result<>();

        _participant.data = getCurrentSession().get(Participant.class, id);
        if (_participant.data == null) {
            _participant.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Participant.class.getSimpleName()});
        }

        return _participant;
    }

    @Transactional(readOnly = true)
    public List<Participant> participants(String disputeId) {
        Result<Dispute> _record = get(disputeId);
        if (!_record.isOK()) {
            return new ArrayList<>();
        }

        Result<DefaultUser> _applicant = userRepository.getBriefByIdOrAccountOrMp(_record.data.getCreatorId(), null);

        List<Participant> _participants = new ArrayList<>();
        _participants.add(new Participant(_applicant.data.getId(), disputeId, _applicant.data.getIdNO(), _applicant.data.getName(), _applicant.data.getMp()));

        _participants.addAll(stream(Participant.class)
                .where(i -> disputeId.equals(i.getDisputeId()))
                .toList());
        return _participants;
    }

    public Result<String> saveParticipant(Participant participant, String operatorId) {
        Result<String> _id = new Result<>();

        Result<Dispute> _record = ((DisputeRepository) AopContext.currentProxy()).permit(participant.getDisputeId(), operatorId);
        if (!_record.isOK()) {
            return _id.pack(_record);
        }

        if (!DefaultValidatorUtil.isIdentityNo(participant.getIdNO())) {
            _id.setCode(Result.ENUM_ERROR.P, 2);
            return _id;
        }

        if (!DefaultValidatorUtil.isChineseName(participant.getName())) {
            _id.setCode(Result.ENUM_ERROR.P, 3);
            return _id;
        }

        if (!DefaultValidatorUtil.isMp(participant.getContact())) {
            _id.setCode(Result.ENUM_ERROR.P, 4);
            return _id;
        }

        if (_record.data.getStatus() != Dispute.ENUM_STATUS.DRAFT) {
            _record.setCode(Result.ENUM_ERROR.B, 1);
            return _id.pack(_record);
        }

        boolean _alreadyExisted = true;

        Result<Participant> _participant = ((DisputeRepository) AopContext.currentProxy()).getParticipant(participant.getId());
        if (!_participant.isOK() && !_participant.getCode().endsWith(Result.DATABASE_RECORD_NOT_FOUND)) {
            return _id.pack(_participant);
        }

        if (!_participant.isOK()) {
            _participant.data = new Participant();

            _alreadyExisted = false;
        }

        BeanUtils.copyProperties(participant, _participant.data, "id", "creatorId", "createTime", "maintainerId", "maintainTime");

        if (_alreadyExisted) {
            _participant.data.setMaintainerId(operatorId);
            _participant.data.setMaintainTime(new Date());
            getCurrentSession().update(_participant.data);
        } else {
            _participant.data.setCreatorId(operatorId);
            _participant.data.setCreateTime(new Date());
            getCurrentSession().save(_participant.data);
        }

        _id.data = participant.getId();
        return _id;
    }

    public Result<Void> removeParticipant(String id, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<Participant> _participant = ((DisputeRepository) AopContext.currentProxy()).getParticipant(id);
        if (!_participant.isOK()) {
            return _success.pack(_participant);
        }

        Result<Dispute> _record = ((DisputeRepository) AopContext.currentProxy()).permit(_participant.data.getDisputeId(), operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        if (_record.data.getStatus() != Dispute.ENUM_STATUS.DRAFT) {
            _record.setCode(Result.ENUM_ERROR.B, 1);
            return _success.pack(_record);
        }

        getCurrentSession().remove(_participant.data);

        return _success;
    }

    public static class Category {

        String id;
        String name;
        List<Category> subCategories;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = StringUtils.trimWhitespace(name);
        }

        public List<Category> getSubCategories() {
            return subCategories;
        }

        public void setSubCategories(List<Category> subCategories) {
            this.subCategories = subCategories;
        }

    }

}