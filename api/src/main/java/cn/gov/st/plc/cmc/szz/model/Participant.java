package cn.gov.st.plc.cmc.szz.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "dispute_participants")
public class Participant extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36, nullable = false)
    String disputeId;

    @Column(length = 36, nullable = false)
    String idNO;

    @Column(length = 36, nullable = false)
    String name;

    @Column(length = 36, nullable = false)
    String contact;

    @Transient
    boolean isApplicant = false;

    public Participant() {
    }

    public Participant(String id, String disputeId, String idNO, String name, String contact) {
        this.id = id;
        this.disputeId = disputeId;
        this.idNO = idNO;
        this.name = name;
        this.contact = contact;
        this.isApplicant = true;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(String disputeId) {
        this.disputeId = disputeId;
    }

    public String getIdNO() {
        return idNO;
    }

    public void setIdNO(String idNO) {
        this.idNO = idNO;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public boolean getIsApplicant() {
        return isApplicant;
    }

    public void setIsApplicant(boolean applicant) {
        isApplicant = applicant;
    }

}