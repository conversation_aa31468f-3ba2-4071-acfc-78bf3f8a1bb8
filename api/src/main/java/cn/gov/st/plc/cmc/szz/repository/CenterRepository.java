package cn.gov.st.plc.cmc.szz.repository;

import cn.gov.st.plc.cmc.szz.model.Center;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;

@Repository
public class CenterRepository extends AbstractEntityRepository<Center> {

    public CenterRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Center.class);
    }

    @Transactional(readOnly = true)
    public List<Center> find(String region) {
        JinqStream<Center> _query = stream(Center.class);

        if (StringUtils.hasLength(region)) {
            _query = _query.where(i -> region.equals(i.getRegion()));
        }

        return _query.toList();
    }

}