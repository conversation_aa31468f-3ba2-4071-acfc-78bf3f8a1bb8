package cn.gov.st.plc.cmc.admin.controller.sys;

import cn.gov.st.plc.cmc.admin.model.sys.Agency;
import cn.gov.st.plc.cmc.admin.repository.sys.AgencyRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.Department;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "agency")
public class AgencyController {

    final AgencyRepository agencyRepository;

    public AgencyController(AgencyRepository agencyRepository) {
        this.agencyRepository = agencyRepository;
    }

    @PostMapping(value = "/tree")
    public Result<List<OrganizationTreeNode>> tree() {
        List<Agency> _items = agencyRepository.subordinates(null, null, 3);

        int _root = _items.stream()
                .min(Comparator.comparing(Agency::getLevel))
                .map(Department::getLevel).orElse(1);
        int _leaf = _items.stream()
                .max(Comparator.comparing(Agency::getLevel))
                .map(Department::getLevel).orElse(_root);

        List<OrganizationTreeNode> _subordinates = _items.stream()
                .filter(j -> _leaf == j.getLevel())
                .map(OrganizationTreeNode::new)
                .collect(Collectors.toList());

        List<OrganizationTreeNode> _superiors;
        while (true) {
            Map<String, List<OrganizationTreeNode>> _groups = _subordinates.stream()
                    .filter(j -> j.getDept().getSuperiorId() != null)
                    .collect(Collectors.groupingBy(j -> j.getDept().getSuperiorId()));
            if (_groups.isEmpty()) {
                break;
            }

            _superiors = new ArrayList<>();
            for (String j : _groups.keySet()) {
                OrganizationTreeNode _superior = _items.stream()
                        .filter(k -> Objects.equals(j, k.getId()))
                        .map(OrganizationTreeNode::new)
                        .findFirst().orElse(null);
                if (_superior != null) {
                    _superior.setSubordinates(_groups.get(j));
                }

                _superiors.add(_superior);
            }

            // 读取父级节点的兄弟叶子节点
            int _lv = _superiors.stream()
                    .map(i -> i.getDept().getLevel())
                    .findFirst().orElse(null);
            List<String> _deptIds = _superiors.stream()
                    .map(i -> i.getDept().getId())
                    .collect(Collectors.toList());
            List<OrganizationTreeNode> _siblings = _items.stream()
                    .filter(i -> CollectionUtils.isEmpty(_deptIds) || !_deptIds.contains(i.getId()))
                    .filter(i -> _lv == i.getLevel())
                    .map(OrganizationTreeNode::new)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(_siblings)) {
                _superiors.addAll(_siblings);
            }

            _subordinates = _superiors;
        }

        List<String> _names = Arrays.asList("金平区", "龙湖区", "潮阳区", "潮南区", "澄海区", "濠江区", "南澳县");
        _superiors = _subordinates.stream()
                .filter(i -> _names.contains(i.getDept().getName()))
                .collect(Collectors.toList());

        Result<List<OrganizationTreeNode>> _nodes = new Result<>();
        _nodes.data = _superiors;
        return _nodes;
    }

    public static class OrganizationTreeNode {

        Agency dept;
        List<OrganizationTreeNode> subordinates;

        public OrganizationTreeNode(Agency dept) {
            this.dept = dept;
        }

        public Agency getDept() {
            return dept;
        }

        public void setDept(Agency dept) {
            this.dept = dept;
        }

        public List<OrganizationTreeNode> getSubordinates() {
            return subordinates;
        }

        public void setSubordinates(List<OrganizationTreeNode> subordinates) {
            this.subordinates = subordinates;
        }

    }

}