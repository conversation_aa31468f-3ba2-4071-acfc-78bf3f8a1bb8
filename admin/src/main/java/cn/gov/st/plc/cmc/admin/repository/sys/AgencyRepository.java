package cn.gov.st.plc.cmc.admin.repository.sys;

import cn.gov.st.plc.cmc.admin.model.sys.Agency;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;

@Transactional(transactionManager = "cmcTransactionManager", readOnly = true)
public class AgencyRepository extends DepartmentRepository<Agency> {

    public static final String ROOT_DEPARTMENT_NAME = "默认";

    // 村居级别
    public static final int RURAL_LEVEL = 3;

    // 网格级别
    public static final int GRID_LEVEL = 4;

    // 应急单元级别
    public static final int EU_LEVEL = 5;


    public AgencyRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            StaffRepository staffRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Agency.class, staffRepository);
    }

    @Transactional(readOnly = true)
    public Result<Agency> getByFullName(String name) {
        Result<Agency> _item = new Result<>();

        _item.data = stream(Agency.class)
                .where(i -> name.equals(i.getFullName()) && i.getIsEnabled())
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Agency.class.getSimpleName()});
        }

        return _item;
    }

}