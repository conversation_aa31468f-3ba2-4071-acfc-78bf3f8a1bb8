package cn.gov.st.plc.cmc.admin.repository.sys;

import cn.gov.st.plc.cmc.admin.model.sys.Staff;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;

@Transactional(transactionManager = "cmcTransactionManager", readOnly = true)
public class StaffRepository extends UserRepository<Staff> {

    public StaffRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, null, null, null, Staff.class);
    }

}