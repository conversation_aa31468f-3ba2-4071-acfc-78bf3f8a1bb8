<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>cn.gov.st.plc.cmc</groupId>
    <artifactId>szz</artifactId>
    <version>1.0.0.RELEASE</version>

    <modules>
        <module>infra</module>
        <module>api</module>
        <module>admin</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <java.version>1.8</java.version>
        <mysql.version>8.4.0</mysql.version>
        <shiro.version>1.13.0</shiro.version>
        <snakeyaml.version>1.33</snakeyaml.version>
        <sparrow.version>1.1.1.0</sparrow.version>
        <sparrow.api.version>1.1.1.1</sparrow.api.version>
        <tomcat.version>9.0.91</tomcat.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chinamobile.sparrow</groupId>
            <artifactId>sparrow-domain</artifactId>
            <version>${sparrow.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.chinamobile.sparrow</groupId>
            <artifactId>sparrow-spring-boot-web-api-starter</artifactId>
            <version>${sparrow.api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-spring-boot-web-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>tomcat-embed-core</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!-- 兼容ShardingSphere、agents-flex -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>4.0.6</version>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring-boot-web-starter</artifactId>
            <version>${shiro.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>
    </dependencies>
</project>